# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project.urls]
homepage = "https://huggingface.co/lerobot"
documentation = "https://huggingface.co/docs/lerobot/index"
source = "https://github.com/huggingface/lerobot"
issues = "https://github.com/huggingface/lerobot/issues"
discord = "https://discord.gg/s3KuuzsPFb"

[project]
name = "lerobot"
version = "0.3.4"
description = "🤗 LeRobot: State-of-the-art Machine Learning for Real-World Robotics in Pytorch"
readme = "README.md"
license = { text = "Apache-2.0" }
requires-python = ">=3.10"
authors = [
    { name = "Rémi Cadène", email = "<EMAIL>" },
    { name = "Simon Alibert", email = "<EMAIL>" },
    { name = "Alexander Soare", email = "<EMAIL>" },
    { name = "Quentin Gallouédec", email = "<EMAIL>" },
    { name = "Steven Palma", email = "<EMAIL>" },
    { name = "Pepijn Kooijmans", email = "<EMAIL>"},
    { name = "Michel Aractingi", email = "<EMAIL>"},
    { name = "Adil Zouitine", email = "<EMAIL>" },
    { name = "Dana Aubakirova", email = "<EMAIL>"},
    { name = "Caroline Pascal", email = "<EMAIL>"},
    { name = "Martino Russi", email = "<EMAIL>"},
    { name = "Thomas Wolf", email = "<EMAIL>" },
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3.10",
    "Topic :: Software Development :: Build Tools",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["lerobot", "huggingface", "robotics",  "machine learning", "artificial intelligence"]

dependencies = [

    # Hugging Face dependencies
    "datasets>=2.19.0,<=3.6.0", # TODO: Bumb dependency
    "diffusers>=0.27.2",
    "huggingface-hub[hf-transfer,cli]>=0.34.2",

    # Core dependencies
    "cmake>=3.29.0.1",
    "einops>=0.8.0",
    "opencv-python-headless>=4.9.0",
    "av>=14.2.0",
    "jsonlines>=4.0.0",
    "packaging>=24.2",
    "pynput>=1.7.7",
    "pyserial>=3.5",
    "wandb>=0.20.0",

    "torch>=2.2.1,<2.8.0", # TODO: Bumb dependency
    "torchcodec>=0.2.1,<0.6.0; sys_platform != 'win32' and (sys_platform != 'linux' or (platform_machine != 'aarch64' and platform_machine != 'arm64' and platform_machine != 'armv7l')) and (sys_platform != 'darwin' or platform_machine != 'x86_64')", # TODO: Bumb dependency
    "torchvision>=0.21.0,<0.23.0", # TODO: Bumb dependency

    "draccus==0.10.0", # TODO: Remove ==
    "gymnasium>=0.29.1,<1.0.0", # TODO: Bumb dependency
    "rerun-sdk>=0.21.0,<0.23.0", # TODO: Bumb dependency

    # Support dependencies
    "deepdiff>=7.0.1,<9.0.0",
    "flask>=3.0.3,<4.0.0",
    "imageio[ffmpeg]>=2.34.0,<3.0.0",
    "termcolor>=2.4.0,<4.0.0",
]

# Optional dependencies
[project.optional-dependencies]

# Common
pygame-dep = ["pygame>=2.5.1"]
placo-dep = ["placo>=0.9.6"]
transformers-dep = ["transformers>=4.50.3,<4.52.0"] # TODO: Bumb dependency
grpcio-dep = ["grpcio==1.73.1", "protobuf==6.31.0"]

# Motors
feetech = ["feetech-servo-sdk>=1.0.0"]
dynamixel = ["dynamixel-sdk>=3.7.31"]

# Robots
gamepad = ["lerobot[pygame-dep]", "hidapi>=0.14.0"]
hopejr = ["lerobot[feetech]", "lerobot[pygame-dep]"]
lekiwi = ["lerobot[feetech]", "pyzmq>=26.2.1"]
kinematics = ["lerobot[placo-dep]"]
intelrealsense = [
    "pyrealsense2>=2.55.1.6486 ; sys_platform != 'darwin'",
    "pyrealsense2-macosx>=2.54 ; sys_platform == 'darwin'",
]
# stretch = [
#     "hello-robot-stretch-body>=0.7.27 ; sys_platform == 'linux'",
#     "pyrender @ git+https://github.com/mmatl/pyrender.git ; sys_platform == 'linux'",
#     "pyrealsense2>=2.55.1.6486 ; sys_platform != 'darwin'"
# ] # TODO: Currently not supported

# Policies
pi0 = ["lerobot[transformers-dep]"]
smolvla = ["lerobot[transformers-dep]", "num2words>=0.5.14", "accelerate>=1.7.0", "safetensors>=0.4.3"]
hilserl = ["lerobot[transformers-dep]", "gym-hil>=0.1.9", "lerobot[grpcio-dep]", "lerobot[placo-dep]"]

# Features
async = ["lerobot[grpcio-dep]", "matplotlib>=3.10.3"]

# Development
dev = ["pre-commit>=3.7.0", "debugpy>=1.8.1", "lerobot[grpcio-dep]", "grpcio-tools==1.73.1"]
test = ["pytest>=8.1.0", "pytest-timeout>=2.4.0", "pytest-cov>=5.0.0", "mock-serial>=0.0.1 ; sys_platform != 'win32'"]
video_benchmark = ["scikit-image>=0.23.2", "pandas>=2.2.2"]

# Simulation
aloha = ["gym-aloha>=0.1.1"]
pusht = ["gym-pusht>=0.1.5", "pymunk>=6.6.0,<7.0.0"] # TODO: Fix pymunk version in gym-pusht instead
xarm = ["gym-xarm>=0.1.1"]

# All
all = [
    "lerobot[dynamixel]",
    "lerobot[gamepad]",
    "lerobot[hopejr]",
    "lerobot[lekiwi]",
    "lerobot[kinematics]",
    "lerobot[intelrealsense]",
    "lerobot[pi0]",
    "lerobot[smolvla]",
    "lerobot[hilserl]",
    "lerobot[async]",
    "lerobot[dev]",
    "lerobot[test]",
    "lerobot[video_benchmark]",
    "lerobot[aloha]",
    "lerobot[pusht]",
    "lerobot[xarm]"
]

[project.scripts]
lerobot-calibrate="lerobot.calibrate:main"
lerobot-find-cameras="lerobot.find_cameras:main"
lerobot-find-port="lerobot.find_port:main"
lerobot-record="lerobot.record:main"
lerobot-replay="lerobot.replay:main"
lerobot-setup-motors="lerobot.setup_motors:main"
lerobot-teleoperate="lerobot.teleoperate:main"
lerobot-eval="lerobot.scripts.eval:main"
lerobot-train="lerobot.scripts.train:main"

# ---------------- Tool Configurations ----------------
[tool.setuptools.packages.find]
where = ["src"]

[tool.ruff]
target-version = "py310"
line-length = 110
exclude = ["tests/artifacts/**/*.safetensors", "*_pb2.py", "*_pb2_grpc.py"]

[tool.ruff.lint]
# E, W: pycodestyle errors and warnings
# F: PyFlakes
# I: isort
# UP: pyupgrade
# B: flake8-bugbear (good practices, potential bugs)
# C4: flake8-comprehensions (more concise comprehensions)
# A: flake8-builtins (shadowing builtins)
# SIM: flake8-simplify
# RUF: Ruff-specific rules
# D: pydocstyle (for docstring style/formatting)
# S: flake8-bandit (some security checks, complements Bandit)
# T20: flake8-print (discourage print statements in production code)
# N: pep8-naming
# TODO: Uncomment rules when ready to use
select = [
    "E", "W", "F", "I", "B", "C4", "T20", "N" # "SIM", "A", "S", "D", "RUF", "UP"
]
ignore = [
    "E501", # Line too long
    "T201", # Print statement found
    "T203", # Pprint statement found
    "B008", # Perform function call in argument defaults
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "F403"]

[tool.ruff.lint.isort]
combine-as-imports = true
known-first-party = ["lerobot"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true

[tool.bandit]
exclude_dirs = [
    "tests",
    "benchmarks",
    "src/lerobot/datasets/push_dataset_to_hub",
    "src/lerobot/datasets/v2/convert_dataset_v1_to_v2",
    "src/lerobot/policies/pi0/conversion_scripts",
    "src/lerobot/scripts/push_dataset_to_hub.py",
]
skips = ["B101", "B311", "B404", "B603", "B615"]

[tool.typos]
default.extend-ignore-re = [
    "(?Rm)^.*(#|//)\\s*spellchecker:disable-line$",                      # spellchecker:disable-line
    "(?s)(#|//)\\s*spellchecker:off.*?\\n\\s*(#|//)\\s*spellchecker:on", # spellchecker:<on|off>
]
default.extend-ignore-identifiers-re = [
    # Add individual words here to ignore them
    "2nd",
    "pn",
    "ser",
    "ein",
]

# TODO: Uncomment when ready to use
# [tool.interrogate]
# ignore-init-module = true
# ignore-init-method = true
# ignore-nested-functions = false
# ignore-magic = false
# ignore-semiprivate = false
# ignore-private = false
# ignore-property-decorators = false
# ignore-module = false
# ignore-setters = false
# fail-under = 80
# output-format = "term-missing"
# color = true
# paths = ["src/lerobot"]

# [tool.mypy]
# python_version = "3.10"
# warn_return_any = true
# warn_unused_configs = true
# ignore_missing_imports = false
