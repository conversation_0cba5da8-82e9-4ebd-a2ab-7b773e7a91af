# SO-100

In the steps below, we explain how to assemble the SO-100 robot.

## Source the parts

Follow this [README](https://github.com/TheRobotStudio/SO-ARM100/blob/main/SO100.md). It contains the bill of materials, with a link to source the parts, as well as the instructions to 3D print the parts. And advise if it's your first time printing or if you don't own a 3D printer.

## Install LeRobot 🤗

To install LeRobot, follow our [Installation Guide](./installation)

In addition to these instructions, you need to install the Feetech SDK:

```bash
pip install -e ".[feetech]"
```

## Configure the motors

**Note:**
Unlike the SO-101, the motor connectors are not easily accessible once the arm is assembled, so the configuration step must be done beforehand.

### 1. Find the USB ports associated with each arm

To find the port for each bus servo adapter, run this script:

```bash
lerobot-find-port
```

<hfoptions id="example">
<hfoption id="Mac">

Example output:

```
Finding all available ports for the MotorBus.
['/dev/tty.usbmodem575E0032081', '/dev/tty.usbmodem575E0031751']
Remove the USB cable from your MotorsBus and press Enter when done.

[...Disconnect corresponding leader or follower arm and press Enter...]

The port of this MotorsBus is /dev/tty.usbmodem575E0032081
Reconnect the USB cable.
```

Where the found port is: `/dev/tty.usbmodem575E0032081` corresponding to your leader or follower arm.

</hfoption>
<hfoption id="Linux">

On Linux, you might need to give access to the USB ports by running:

```bash
sudo chmod 666 /dev/ttyACM0
sudo chmod 666 /dev/ttyACM1
```

Example output:

```
Finding all available ports for the MotorBus.
['/dev/ttyACM0', '/dev/ttyACM1']
Remove the usb cable from your MotorsBus and press Enter when done.

[...Disconnect corresponding leader or follower arm and press Enter...]

The port of this MotorsBus is /dev/ttyACM1
Reconnect the USB cable.
```

Where the found port is: `/dev/ttyACM1` corresponding to your leader or follower arm.

</hfoption>
</hfoptions>

### 2. Set the motors ids and baudrates

Each motor is identified by a unique id on the bus. When brand new, motors usually come with a default id of `1`. For the communication to work properly between the motors and the controller, we first need to set a unique, different id to each motor. Additionally, the speed at which data is transmitted on the bus is determined by the baudrate. In order to talk to each other, the controller and all the motors need to be configured with the same baudrate.

To that end, we first need to connect to each motor individually with the controller in order to set these. Since we will write these parameters in the non-volatile section of the motors' internal memory (EEPROM), we'll only need to do this once.

If you are repurposing motors from another robot, you will probably also need to perform this step as the ids and baudrate likely won't match.

#### Follower

Connect the usb cable from your computer and the power supply to the follower arm's controller board. Then, run the following command or run the API example with the port you got from the previous step. You'll also need to give your leader arm a name with the `id` parameter.

For a visual reference on how to set the motor ids please refer to [this video](https://huggingface.co/docs/lerobot/en/so101#setup-motors-video) where we follow the process for the SO101 arm.

<hfoptions id="setup_motors">
<hfoption id="Command">

```bash
lerobot-setup-motors \
    --robot.type=so100_follower \
    --robot.port=/dev/tty.usbmodem585A0076841  # <- paste here the port found at previous step
```

</hfoption>
<hfoption id="API example">

<!-- prettier-ignore-start -->
```python
from lerobot.robots.so100_follower import SO100Follower, SO100FollowerConfig

config = SO100FollowerConfig(
    port="/dev/tty.usbmodem585A0076841",
    id="my_awesome_follower_arm",
)
follower = SO100Follower(config)
follower.setup_motors()
```
<!-- prettier-ignore-end -->

</hfoption>
</hfoptions>

You should see the following instruction

```
Connect the controller board to the 'gripper' motor only and press enter.
```

As instructed, plug the gripper's motor. Make sure it's the only motor connected to the board, and that the motor itself is not yet daisy-chained to any other motor. As you press `[Enter]`, the script will automatically set the id and baudrate for that motor.

<details>
<summary>Troubleshooting</summary>

If you get an error at that point, check your cables and make sure they are plugged in properly:

<ul>
  <li>Power supply</li>
  <li>USB cable between your computer and the controller board</li>
  <li>The 3-pin cable from the controller board to the motor</li>
</ul>

If you are using a Waveshare controller board, make sure that the two jumpers are set on the `B` channel (USB).

</details>

You should then see the following message:

```
'gripper' motor id set to 6
```

Followed by the next instruction:

```
Connect the controller board to the 'wrist_roll' motor only and press enter.
```

You can disconnect the 3-pin cable from the controller board, but you can leave it connected to the gripper motor on the other end, as it will already be in the right place. Now, plug in another 3-pin cable to the wrist roll motor and connect it to the controller board. As with the previous motor, make sure it is the only motor connected to the board and that the motor itself isn't connected to any other one.

Repeat the operation for each motor as instructed.

> [!TIP]
> Check your cabling at each step before pressing Enter. For instance, the power supply cable might disconnect as you manipulate the board.

When you are done, the script will simply finish, at which point the motors are ready to be used. You can now plug the 3-pin cable from each motor to the next one, and the cable from the first motor (the 'shoulder pan' with id=1) to the controller board, which can now be attached to the base of the arm.

#### Leader

Do the same steps for the leader arm.

<hfoptions id="setup_motors">
<hfoption id="Command">
```bash
lerobot-setup-motors \
    --teleop.type=so100_leader \
    --teleop.port=/dev/tty.usbmodem575E0031751  # <- paste here the port found at previous step
```
</hfoption>
<hfoption id="API example">

<!-- prettier-ignore-start -->
```python
from lerobot.teleoperators.so100_leader import SO100Leader, SO100LeaderConfig

config = SO100LeaderConfig(
    port="/dev/tty.usbmodem585A0076841",
    id="my_awesome_leader_arm",
)
leader = SO100Leader(config)
leader.setup_motors()
```
<!-- prettier-ignore-end -->

</hfoption>
</hfoptions>

## Step-by-Step Assembly Instructions

## Remove the gears of the 6 leader motors

<details>
<summary><strong>Video removing gears</strong></summary>

<div class="video-container">
  <video controls width="600">
    <source
      src="https://github.com/user-attachments/assets/0c95b88c-5b85-413d-ba19-aee2f864f2a7"
      type="video/mp4"
    />
  </video>
</div>

</details>

Follow the video for removing gears. You need to remove the gear for the motors of the leader arm. As a result, you will only use the position encoding of the motor and reduce friction to more easily operate the leader arm.

### Clean Parts

Remove all support material from the 3D-printed parts. The easiest way to do this is using a small screwdriver to get underneath the support material.

### Additional Guidance

<details>
<summary><strong>Video assembling arms</strong></summary>

<div class="video-container">
  <video controls width="600">
    <source
      src="https://github.com/user-attachments/assets/488a39de-0189-4461-9de3-05b015f90cca"
      type="video/mp4"
    />
  </video>
</div>

</details>

**Note:**
This video provides visual guidance for assembling the arms, but it doesn't specify when or how to do the wiring. Inserting the cables beforehand is much easier than doing it afterward. The first arm may take a bit more than 1 hour to assemble, but once you get used to it, you can assemble the second arm in under 1 hour.

---

### First Motor

**Step 2: Insert Wires**

- Insert two wires into the first motor.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_1.webp"
  style="height:300px;"
/>

**Step 3: Install in Base**

- Place the first motor into the base.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_2.webp"
  style="height:300px;"
/>

**Step 4: Secure Motor**

- Fasten the motor with 4 screws. Two from the bottom and two from top.

**Step 5: Attach Motor Holder**

- Slide over the first motor holder and fasten it using two screws (one on each side).

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_4.webp"
  style="height:300px;"
/>

**Step 6: Attach Motor Horns**

- Install both motor horns, securing the top horn with a screw. Try not to move the motor position when attaching the motor horn, especially for the leader arms, where we removed the gears.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_5.webp"
  style="height:300px;"
/>

<details>
  <summary>
    <strong>Video adding motor horn</strong>
  </summary>
  <video src="https://github.com/user-attachments/assets/ef3391a4-ad05-4100-b2bd-1699bf86c969"></video>
</details>

**Step 7: Attach Shoulder Part**

- Route one wire to the back of the robot and the other to the left or towards you (see photo).
- Attach the shoulder part.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_6.webp"
  style="height:300px;"
/>

**Step 8: Secure Shoulder**

- Tighten the shoulder part with 4 screws on top and 4 on the bottom
  _(access bottom holes by turning the shoulder)._

---

### Second Motor Assembly

**Step 9: Install Motor 2**

- Slide the second motor in from the top and link the wire from motor 1 to motor 2.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_8.webp"
  style="height:300px;"
/>

**Step 10: Attach Shoulder Holder**

- Add the shoulder motor holder.
- Ensure the wire from motor 1 to motor 2 goes behind the holder while the other wire is routed upward (see photo).
- This part can be tight to assemble, you can use a workbench like the image or a similar setup to push the part around the motor.

<div style="display: flex;">
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_9.webp"
    style="height:250px;"
  />
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_10.webp"
    style="height:250px;"
  />
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_12.webp"
    style="height:250px;"
  />
</div>

**Step 11: Secure Motor 2**

- Fasten the second motor with 4 screws.

**Step 12: Attach Motor Horn**

- Attach both motor horns to motor 2, again use the horn screw.

**Step 13: Attach Base**

- Install the base attachment using 2 screws.

<img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_11.webp" style="height:300px;">

**Step 14: Attach Upper Arm**

- Attach the upper arm with 4 screws on each side.

<img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_13.webp" style="height:300px;">

---

### Third Motor Assembly

**Step 15: Install Motor 3**

- Route the motor cable from motor 2 through the cable holder to motor 3, then secure motor 3 with 4 screws.

**Step 16: Attach Motor Horn**

- Attach both motor horns to motor 3 and secure one again with a horn screw.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_14.webp"
  style="height:300px;"
/>

**Step 17: Attach Forearm**

- Connect the forearm to motor 3 using 4 screws on each side.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_15.webp"
  style="height:300px;"
/>

---

### Fourth Motor Assembly

**Step 18: Install Motor 4**

- Slide in motor 4, attach the cable from motor 3, and secure the cable in its holder with a screw.

<div style="display: flex;">
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_16.webp"
    style="height:300px;"
  />
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_19.webp"
    style="height:300px;"
  />
</div>

**Step 19: Attach Motor Holder 4**

- Install the fourth motor holder (a tight fit). Ensure one wire is routed upward and the wire from motor 3 is routed downward (see photo).

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_17.webp"
  style="height:300px;"
/>

**Step 20: Secure Motor 4 & Attach Horn**

- Fasten motor 4 with 4 screws and attach its motor horns, use for one a horn screw.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_18.webp"
  style="height:300px;"
/>

---

### Wrist Assembly

**Step 21: Install Motor 5**

- Insert motor 5 into the wrist holder and secure it with 2 front screws.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_20.webp"
  style="height:300px;"
/>

**Step 22: Attach Wrist**

- Connect the wire from motor 4 to motor 5. And already insert the other wire for the gripper.
- Secure the wrist to motor 4 using 4 screws on both sides.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_22.webp"
  style="height:300px;"
/>

**Step 23: Attach Wrist Horn**

- Install only one motor horn on the wrist motor and secure it with a horn screw.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_23.webp"
  style="height:300px;"
/>

---

### Follower Configuration

**Step 24: Attach Gripper**

- Attach the gripper to motor 5.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_24.webp"
  style="height:300px;"
/>

**Step 25: Install Gripper Motor**

- Insert the gripper motor, connect the motor wire from motor 5 to motor 6, and secure it with 3 screws on each side.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_25.webp"
  style="height:300px;"
/>

**Step 26: Attach Gripper Horn & Claw**

- Attach the motor horns and again use a horn screw.
- Install the gripper claw and secure it with 4 screws on both sides.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_26.webp"
  style="height:300px;"
/>

**Step 27: Mount Controller**

- Attach the motor controller to the back of the robot.

<div style="display: flex;">
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_27.webp"
    style="height:300px;"
  />
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_28.webp"
    style="height:300px;"
  />
</div>

_Assembly complete – proceed to Leader arm assembly._

---

### Leader Configuration

For the leader configuration, perform **Steps 1–23**. Make sure that you removed the motor gears from the motors.

**Step 24: Attach Leader Holder**

- Mount the leader holder onto the wrist and secure it with a screw.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_29.webp"
  style="height:300px;"
/>

**Step 25: Attach Handle**

- Attach the handle to motor 5 using 4 screws.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_30.webp"
  style="height:300px;"
/>

**Step 26: Install Gripper Motor**

- Insert the gripper motor, secure it with 3 screws on each side, attach a motor horn using a horn screw, and connect the motor wire.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_31.webp"
  style="height:300px;"
/>

**Step 27: Attach Trigger**

- Attach the follower trigger with 4 screws.

<img
  src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_32.webp"
  style="height:300px;"
/>

**Step 28: Mount Controller**

- Attach the motor controller to the back of the robot.

<div style="display: flex;">
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_27.webp"
    style="height:300px;"
  />
  <img
    src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/lerobot/so100_assembly_28.webp"
    style="height:300px;"
  />
</div>

## Calibrate

Next, you'll need to calibrate your robot to ensure that the leader and follower arms have the same position values when they are in the same physical position.
The calibration process is very important because it allows a neural network trained on one robot to work on another.

#### Follower

Run the following command or API example to calibrate the follower arm:

<hfoptions id="calibrate_follower">
<hfoption id="Command">

```bash
lerobot-calibrate \
    --robot.type=so100_follower \
    --robot.port=/dev/tty.usbmodem58760431551 \ # <- The port of your robot
    --robot.id=my_awesome_follower_arm # <- Give the robot a unique name
```

</hfoption>
<hfoption id="API example">

<!-- prettier-ignore-start -->
```python
from lerobot.robots.so100_follower import SO100FollowerConfig, SO100Follower

config = SO100FollowerConfig(
    port="/dev/tty.usbmodem585A0076891",
    id="my_awesome_follower_arm",
)

follower = SO100Follower(config)
follower.connect(calibrate=False)
follower.calibrate()
follower.disconnect()
```
<!-- prettier-ignore-end -->

</hfoption>
</hfoptions>

We unified the calibration method for most robots. Thus, the calibration steps for this SO100 arm are the same as the steps for the Koch and SO101. First, we have to move the robot to the position where each joint is in the middle of its range, then we press `Enter`. Secondly, we move all joints through their full range of motion. A video of this same process for the SO101 as reference can be found [here](https://huggingface.co/docs/lerobot/en/so101#calibration-video)

#### Leader

Do the same steps to calibrate the leader arm, run the following command or API example:

<hfoptions id="calibrate_leader">
<hfoption id="Command">

```bash
lerobot-calibrate \
    --teleop.type=so100_leader \
    --teleop.port=/dev/tty.usbmodem58760431551 \ # <- The port of your robot
    --teleop.id=my_awesome_leader_arm # <- Give the robot a unique name
```

</hfoption>
<hfoption id="API example">

<!-- prettier-ignore-start -->
```python
from lerobot.teleoperators.so100_leader import SO100LeaderConfig, SO100Leader

config = SO100LeaderConfig(
    port="/dev/tty.usbmodem58760431551",
    id="my_awesome_leader_arm",
)

leader = SO100Leader(config)
leader.connect(calibrate=False)
leader.calibrate()
leader.disconnect()
```
<!-- prettier-ignore-end -->

</hfoption>
</hfoptions>

Congrats 🎉, your robot is all set to learn a task on its own. Start training it by following this tutorial: [Getting started with real-world robots](./getting_started_real_world_robot)

> [!TIP]
> If you have any questions or need help, please reach out on [Discord](https://discord.com/invite/s3KuuzsPFb).
