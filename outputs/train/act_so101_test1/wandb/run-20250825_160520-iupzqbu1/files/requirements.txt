setuptools==80.9.0
wheel==0.45.1
pip==25.2
wcwidth==0.2.13
pytz==2025.2
pyserial==3.5
nvidia-cusparselt-cu12==0.6.3
mpmath==1.3.0
Farama-Notifications==0.0.4
zipp==3.23.0
xxhash==3.5.0
urllib3==2.5.0
tzdata==2025.2
typing_extensions==4.14.1
triton==3.3.1
tqdm==4.67.1
torchcodec==0.5
toml==0.10.2
termcolor==3.1.0
sympy==1.14.0
smmap==5.0.2
six==1.17.0
safetensors==0.6.2
regex==2025.7.34
PyYAML==6.0.2
pyarrow==21.0.0
psutil==7.0.0
protobuf==6.31.0
propcache==0.3.2
prompt_toolkit==3.0.51
platformdirs==4.3.8
pillow==11.3.0
pfzy==0.3.4
packaging==25.0
orderly-set==5.5.0
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
numpy==2.2.6
networkx==3.4.2
mypy_extensions==1.1.0
mergedeep==1.3.4
MarkupSafe==3.0.2
itsdangerous==2.2.0
imageio-ffmpeg==0.6.0
idna==3.10
hf-xet==1.1.8
hf_transfer==0.1.9
fsspec==2025.3.0
frozenlist==1.7.0
filelock==3.19.1
evdev==1.9.2
einops==0.8.1
dill==0.3.8
cmake==4.1.0
cloudpickle==3.1.1
click==8.2.1
charset-normalizer==3.4.3
certifi==2025.8.3
blinker==1.9.0
av==15.0.0
attrs==25.3.0
async-timeout==5.0.1
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
Werkzeug==3.1.3
typing-inspection==0.4.1
typing-inspect==0.9.0
sentry-sdk==2.35.0
rerun-sdk==0.22.1
requests==2.32.5
pyyaml-include==1.4.1
python-xlib==0.33
python-dateutil==2.9.0.post0
pydantic_core==2.33.2
opencv-python-headless==*********
nvidia-cusparse-cu12==********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
multiprocess==0.70.16
multidict==6.6.4
jsonlines==4.0.0
Jinja2==3.1.6
inquirerpy==0.3.4
importlib_metadata==8.7.0
imageio==2.37.0
gymnasium==0.29.1
gitdb==4.0.12
deepdiff==8.6.0
aiosignal==1.4.0
yarl==1.20.1
pynput==1.8.1
pydantic==2.11.7
pandas==2.3.2
nvidia-cusolver-cu12==********
huggingface-hub==0.34.4
GitPython==3.1.45
Flask==3.1.2
draccus==0.10.0
wandb==0.21.1
torch==2.7.1
diffusers==0.35.1
aiohttp==3.12.15
torchvision==0.22.1
datasets==3.6.0
lerobot==0.3.4
u-msgpack-python==2.8.0
PyOpenGL==3.1.10
pure_eval==0.2.3
ptyprocess==0.7.0
glfw==2.9.0
docopt==0.6.2
distlib==0.4.0
wrapt==1.17.3
virtualenv==20.34.0
traitlets==5.14.3
tornado==6.5.2
tomli==2.2.1
tifffile==2025.5.10
shapely==2.1.1
scipy==1.15.3
pyzmq==27.0.2
pyrealsense2==2.56.5.9235
pyparsing==3.2.3
pyngrok==7.3.0
Pygments==2.19.2
pygame==2.6.1
pycparser==2.22
pluggy==1.6.0
pexpect==4.9.0
parso==0.8.5
opencv-python==*********
num2words==0.5.14
nodeenv==1.9.1
mock-serial==0.0.1
lxml==6.0.1
lazy_loader==0.4
kiwisolver==1.4.9
ischedule==1.2.7
iniconfig==2.1.0
identify==2.6.13
hidapi==0.14.0.post4
grpcio==1.73.1
fonttools==4.59.1
feetech-servo-sdk==1.0.0
executing==2.2.0
exceptiongroup==1.3.0
dynamixel-sdk==3.7.31
decorator==5.2.1
debugpy==1.8.16
cycler==0.12.1
coverage==7.10.5
contourpy==1.3.2
cfgv==3.4.0
asttokens==3.0.0
absl-py==2.3.1
stack-data==0.6.3
scikit-image==0.25.2
pytest==8.4.1
pre_commit==4.3.0
pettingzoo==1.24.3
mujoco==2.3.7
matplotlib-inline==0.1.7
matplotlib==3.10.5
labmaze==1.0.6
jedi==0.19.2
grpcio-tools==1.73.1
dm-tree==0.1.9
cmeel==0.57.3
cffi==1.17.1
tokenizers==0.21.4
rhoban-cmeel-jsoncpp==*******
pytest-timeout==2.4.0
pytest-cov==6.2.1
pymunk==6.11.1
ipython==8.37.0
gymnasium-robotics==1.2.4
gym-hil==0.1.10
dm-env==1.6
cmeel-zlib==1.3.1
cmeel-tinyxml2==10.0.0
cmeel-qhull==*******
cmeel-octomap==1.10.0
cmeel-console-bridge==*******
cmeel-boost==********
transformers==4.51.3
meshcat==0.3.2
gym-xarm==0.1.1
gym-pusht==0.1.5
eiquadprog==1.2.9
eigenpy==3.10.3
dm-control==1.0.14
cmeel-urdfdom==4.0.1
cmeel-assimp==*******
accelerate==1.10.0
gym-aloha==0.1.1
coal-library==3.0.1
pin==3.4.0
placo==0.9.14
lerobot==0.3.4
