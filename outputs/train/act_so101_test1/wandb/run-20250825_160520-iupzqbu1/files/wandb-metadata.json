{"os": "Linux-5.4.0-72-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.18", "startedAt": "2025-08-25T08:05:20.781063Z", "args": ["--dataset.repo_id=lerobot/aloha_sim_transfer_cube_scripted", "--policy.type=act", "--output_dir=outputs/train/act_so101_test", "--job_name=act_so101_test", "--policy.device=cuda", "--wandb.enable=true", "--policy.push_to_hub=False"], "program": "/mnt/vdb1/envs/lerobot/bin/lerobot-train", "root": "outputs/train/act_so101_test", "host": "VM-59-6-<PERSON><PERSON><PERSON><PERSON>", "executable": "/mnt/vdb1/envs/lerobot/bin/python3.10", "cpu_count": 24, "cpu_count_logical": 24, "gpu": "NVIDIA H800", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "100706693120"}, "gpu_nvidia": [{"name": "NVIDIA H800", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-8f6a2715-cf0e-6220-3ad7-70072cbe7901"}], "cudaVersion": "12.4", "writerId": "fyjba4gm1fgo5xdf9rzmkb0zkl53yk1p"}