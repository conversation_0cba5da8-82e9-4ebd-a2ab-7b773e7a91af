_wandb:
    value:
        cli_version: 0.21.1
        e:
            fyjba4gm1fgo5xdf9rzmkb0zkl53yk1p:
                args:
                    - --dataset.repo_id=lerobot/aloha_sim_transfer_cube_scripted
                    - --policy.type=act
                    - --output_dir=outputs/train/act_so101_test
                    - --job_name=act_so101_test
                    - --policy.device=cuda
                    - --wandb.enable=true
                    - --policy.push_to_hub=False
                cpu_count: 24
                cpu_count_logical: 24
                cudaVersion: "12.4"
                disk:
                    /:
                        total: "************"
                        used: "************"
                executable: /mnt/vdb1/envs/lerobot/bin/python3.10
                gpu: NVIDIA H800
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Hopper
                      cudaCores: 16896
                      memoryTotal: "85520809984"
                      name: NVIDIA H800
                      uuid: GPU-8f6a2715-cf0e-6220-3ad7-70072cbe7901
                host: VM-59-6-ubuntu
                memory:
                    total: "100706693120"
                os: Linux-5.4.0-72-generic-x86_64-with-glibc2.35
                program: /mnt/vdb1/envs/lerobot/bin/lerobot-train
                python: CPython 3.10.18
                root: outputs/train/act_so101_test
                startedAt: "2025-08-25T08:05:20.781063Z"
                writerId: fyjba4gm1fgo5xdf9rzmkb0zkl53yk1p
        m: []
        python_version: 3.10.18
        t:
            "1":
                - 1
                - 41
                - 49
                - 51
            "2":
                - 1
                - 41
                - 49
                - 51
            "3":
                - 13
                - 15
                - 16
                - 61
            "4": 3.10.18
            "5": 0.21.1
            "10":
                - 21
            "12": 0.21.1
            "13": linux-x86_64
batch_size:
    value: 8
dataset:
    value:
        episodes: null
        image_transforms:
            enable: false
            max_num_transforms: 3
            random_order: false
            tfs:
                brightness:
                    kwargs:
                        brightness:
                            - 0.8
                            - 1.2
                    type: ColorJitter
                    weight: 1
                contrast:
                    kwargs:
                        contrast:
                            - 0.8
                            - 1.2
                    type: ColorJitter
                    weight: 1
                hue:
                    kwargs:
                        hue:
                            - -0.05
                            - 0.05
                    type: ColorJitter
                    weight: 1
                saturation:
                    kwargs:
                        saturation:
                            - 0.5
                            - 1.5
                    type: ColorJitter
                    weight: 1
                sharpness:
                    kwargs:
                        sharpness:
                            - 0.5
                            - 1.5
                    type: SharpnessJitter
                    weight: 1
        repo_id: lerobot/aloha_sim_transfer_cube_scripted
        revision: null
        root: null
        use_imagenet_stats: true
        video_backend: torchcodec
env:
    value: null
eval:
    value:
        batch_size: 50
        n_episodes: 50
        use_async_envs: false
eval_freq:
    value: 20000
job_name:
    value: act_so101_test
log_freq:
    value: 200
num_workers:
    value: 4
optimizer:
    value:
        betas:
            - 0.9
            - 0.999
        eps: 1e-08
        grad_clip_norm: 10
        lr: 1e-05
        type: adamw
        weight_decay: 0.0001
output_dir:
    value: outputs/train/act_so101_test
policy:
    value:
        chunk_size: 100
        device: cuda
        dim_feedforward: 3200
        dim_model: 512
        dropout: 0.1
        feedforward_activation: relu
        kl_weight: 10
        latent_dim: 32
        license: null
        n_action_steps: 100
        n_decoder_layers: 1
        n_encoder_layers: 4
        n_heads: 8
        n_obs_steps: 1
        n_vae_encoder_layers: 4
        normalization_mapping:
            ACTION: MEAN_STD
            STATE: MEAN_STD
            VISUAL: MEAN_STD
        optimizer_lr: 1e-05
        optimizer_lr_backbone: 1e-05
        optimizer_weight_decay: 0.0001
        pre_norm: false
        pretrained_backbone_weights: ResNet18_Weights.IMAGENET1K_V1
        private: null
        push_to_hub: false
        replace_final_stride_with_dilation: false
        repo_id: null
        tags: null
        temporal_ensemble_coeff: null
        type: act
        use_amp: false
        use_vae: true
        vision_backbone: resnet18
resume:
    value: false
save_checkpoint:
    value: true
save_freq:
    value: 20000
scheduler:
    value: null
seed:
    value: 1000
steps:
    value: 100000
use_policy_training_preset:
    value: true
wandb:
    value:
        disable_artifact: false
        enable: true
        entity: null
        mode: null
        notes: null
        project: lerobot
        run_id: null
