2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_setup.py:_flush():80] Current SDK version is 0.21.1
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_setup.py:_flush():80] Configure stats pid to 3795255
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/myrobot/wandb/settings
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_init.py:setup_run_log_directory():703] Logging user logs to outputs/train/act_so101_test/wandb/run-20250825_160520-iupzqbu1/logs/debug.log
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to outputs/train/act_so101_test/wandb/run-20250825_160520-iupzqbu1/logs/debug-internal.log
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_init.py:init():830] calling init triggers
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'dataset': {'repo_id': 'lerobot/aloha_sim_transfer_cube_scripted', 'root': None, 'episodes': None, 'image_transforms': {'enable': False, 'max_num_transforms': 3, 'random_order': False, 'tfs': {'brightness': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'brightness': [0.8, 1.2]}}, 'contrast': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'contrast': [0.8, 1.2]}}, 'saturation': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'saturation': [0.5, 1.5]}}, 'hue': {'weight': 1.0, 'type': 'ColorJitter', 'kwargs': {'hue': [-0.05, 0.05]}}, 'sharpness': {'weight': 1.0, 'type': 'SharpnessJitter', 'kwargs': {'sharpness': [0.5, 1.5]}}}}, 'revision': None, 'use_imagenet_stats': True, 'video_backend': 'torchcodec'}, 'env': None, 'policy': {'type': 'act', 'n_obs_steps': 1, 'normalization_mapping': {'VISUAL': <NormalizationMode.MEAN_STD: 'MEAN_STD'>, 'STATE': <NormalizationMode.MEAN_STD: 'MEAN_STD'>, 'ACTION': <NormalizationMode.MEAN_STD: 'MEAN_STD'>}, 'input_features': {}, 'output_features': {}, 'device': 'cuda', 'use_amp': False, 'push_to_hub': False, 'repo_id': None, 'private': None, 'tags': None, 'license': None, 'chunk_size': 100, 'n_action_steps': 100, 'vision_backbone': 'resnet18', 'pretrained_backbone_weights': 'ResNet18_Weights.IMAGENET1K_V1', 'replace_final_stride_with_dilation': False, 'pre_norm': False, 'dim_model': 512, 'n_heads': 8, 'dim_feedforward': 3200, 'feedforward_activation': 'relu', 'n_encoder_layers': 4, 'n_decoder_layers': 1, 'use_vae': True, 'latent_dim': 32, 'n_vae_encoder_layers': 4, 'temporal_ensemble_coeff': None, 'dropout': 0.1, 'kl_weight': 10.0, 'optimizer_lr': 1e-05, 'optimizer_weight_decay': 0.0001, 'optimizer_lr_backbone': 1e-05}, 'output_dir': 'outputs/train/act_so101_test', 'job_name': 'act_so101_test', 'resume': False, 'seed': 1000, 'num_workers': 4, 'batch_size': 8, 'steps': 100000, 'eval_freq': 20000, 'log_freq': 200, 'save_checkpoint': True, 'save_freq': 20000, 'use_policy_training_preset': True, 'optimizer': {'type': 'adamw', 'lr': 1e-05, 'weight_decay': 0.0001, 'grad_clip_norm': 10.0, 'betas': [0.9, 0.999], 'eps': 1e-08}, 'scheduler': None, 'eval': {'n_episodes': 50, 'batch_size': 50, 'use_async_envs': False}, 'wandb': {'enable': True, 'disable_artifact': False, 'project': 'lerobot', 'entity': None, 'notes': None, 'run_id': None, 'mode': None}, '_wandb': {}}
2025-08-25 16:05:20,782 INFO    MainThread:3795255 [wandb_init.py:init():871] starting backend
2025-08-25 16:05:20,988 INFO    MainThread:3795255 [wandb_init.py:init():874] sending inform_init request
2025-08-25 16:05:20,991 INFO    MainThread:3795255 [wandb_init.py:init():882] backend started and connected
2025-08-25 16:05:20,993 INFO    MainThread:3795255 [wandb_init.py:init():953] updated telemetry
2025-08-25 16:05:20,994 INFO    MainThread:3795255 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-25 16:05:22,004 INFO    MainThread:3795255 [wandb_init.py:init():1029] starting run threads in backend
2025-08-25 16:05:22,140 INFO    MainThread:3795255 [wandb_run.py:_console_start():2494] atexit reg
2025-08-25 16:05:22,140 INFO    MainThread:3795255 [wandb_run.py:_redirect():2342] redirect: wrap_raw
2025-08-25 16:05:22,140 INFO    MainThread:3795255 [wandb_run.py:_redirect():2411] Wrapping output streams.
2025-08-25 16:05:22,140 INFO    MainThread:3795255 [wandb_run.py:_redirect():2434] Redirects installed.
2025-08-25 16:05:22,142 INFO    MainThread:3795255 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-25 17:08:24,572 INFO    MsgRouterThr:3795255 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 2 handles.
