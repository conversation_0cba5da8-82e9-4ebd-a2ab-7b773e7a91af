{"dataset": {"repo_id": "lerobot/aloha_sim_transfer_cube_scripted", "root": null, "episodes": null, "image_transforms": {"enable": false, "max_num_transforms": 3, "random_order": false, "tfs": {"brightness": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"brightness": [0.8, 1.2]}}, "contrast": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"contrast": [0.8, 1.2]}}, "saturation": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"saturation": [0.5, 1.5]}}, "hue": {"weight": 1.0, "type": "ColorJitter", "kwargs": {"hue": [-0.05, 0.05]}}, "sharpness": {"weight": 1.0, "type": "SharpnessJitter", "kwargs": {"sharpness": [0.5, 1.5]}}}}, "revision": null, "use_imagenet_stats": true, "video_backend": "torchcodec"}, "env": null, "policy": {"type": "act", "n_obs_steps": 1, "normalization_mapping": {"VISUAL": "MEAN_STD", "STATE": "MEAN_STD", "ACTION": "MEAN_STD"}, "input_features": {"observation.images.top": {"type": "VISUAL", "shape": [3, 480, 640]}, "observation.state": {"type": "STATE", "shape": [14]}}, "output_features": {"action": {"type": "ACTION", "shape": [14]}}, "device": "cuda", "use_amp": false, "push_to_hub": false, "repo_id": null, "private": null, "tags": null, "license": null, "chunk_size": 100, "n_action_steps": 100, "vision_backbone": "resnet18", "pretrained_backbone_weights": "ResNet18_Weights.IMAGENET1K_V1", "replace_final_stride_with_dilation": false, "pre_norm": false, "dim_model": 512, "n_heads": 8, "dim_feedforward": 3200, "feedforward_activation": "relu", "n_encoder_layers": 4, "n_decoder_layers": 1, "use_vae": true, "latent_dim": 32, "n_vae_encoder_layers": 4, "temporal_ensemble_coeff": null, "dropout": 0.1, "kl_weight": 10.0, "optimizer_lr": 1e-05, "optimizer_weight_decay": 0.0001, "optimizer_lr_backbone": 1e-05}, "output_dir": "outputs/train/act_so101_test", "job_name": "act_so101_test", "resume": false, "seed": 1000, "num_workers": 4, "batch_size": 8, "steps": 100000, "eval_freq": 20000, "log_freq": 200, "save_checkpoint": true, "save_freq": 20000, "use_policy_training_preset": true, "optimizer": {"type": "adamw", "lr": 1e-05, "weight_decay": 0.0001, "grad_clip_norm": 10.0, "betas": [0.9, 0.999], "eps": 1e-08}, "scheduler": null, "eval": {"n_episodes": 50, "batch_size": 50, "use_async_envs": false}, "wandb": {"enable": true, "disable_artifact": false, "project": "<PERSON><PERSON><PERSON>", "entity": null, "notes": null, "run_id": "iupzqbu1", "mode": null}}