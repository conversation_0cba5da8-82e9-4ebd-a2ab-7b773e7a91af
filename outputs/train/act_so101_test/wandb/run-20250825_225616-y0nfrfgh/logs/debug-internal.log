{"time":"2025-08-25T22:56:16.752218555+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.1"}
{"time":"2025-08-25T22:56:17.355157944+08:00","level":"INFO","msg":"stream: created new stream","id":"y0nfrfgh"}
{"time":"2025-08-25T22:56:17.355204494+08:00","level":"INFO","msg":"stream: started","id":"y0nfrfgh"}
{"time":"2025-08-25T22:56:17.355250227+08:00","level":"INFO","msg":"writer: started","stream_id":"y0nfrfgh"}
{"time":"2025-08-25T22:56:17.355254465+08:00","level":"INFO","msg":"sender: started","stream_id":"y0nfrfgh"}
{"time":"2025-08-25T22:56:17.355302639+08:00","level":"INFO","msg":"handler: started","stream_id":"y0nfrfgh"}
{"time":"2025-08-26T00:17:18.320712199+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-26T00:22:51.456491588+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/xxll/lerobot/y0nfrfgh/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-26T00:29:23.7142475+08:00","level":"INFO","msg":"stream: closing","id":"y0nfrfgh"}
{"time":"2025-08-26T00:29:47.065325654+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-26T00:29:47.525340643+08:00","level":"INFO","msg":"handler: closed","stream_id":"y0nfrfgh"}
{"time":"2025-08-26T00:29:47.525454832+08:00","level":"INFO","msg":"sender: closed","stream_id":"y0nfrfgh"}
{"time":"2025-08-26T00:29:47.525473771+08:00","level":"INFO","msg":"stream: closed","id":"y0nfrfgh"}
