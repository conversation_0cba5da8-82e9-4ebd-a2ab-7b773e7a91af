{"os": "Linux-5.4.0-72-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.18", "startedAt": "2025-08-26T14:08:25.654087Z", "args": ["--policy.path=lerobot/smolvla_base", "--dataset.repo_id=lerobot/svla_so100_pickplace", "--batch_size=64", "--steps=20000", "--output_dir=outputs/train/my_smolvla", "--job_name=my_smolvla_training", "--policy.device=cuda", "--wandb.enable=true", "--policy.push_to_hub=false"], "program": "/mnt/vdb1/envs/lerobot/bin/lerobot-train", "root": "outputs/train/my_smolvla", "host": "VM-59-6-<PERSON><PERSON><PERSON><PERSON>", "executable": "/mnt/vdb1/envs/lerobot/bin/python3.10", "cpu_count": 24, "cpu_count_logical": 24, "gpu": "NVIDIA H800", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "100706693120"}, "gpu_nvidia": [{"name": "NVIDIA H800", "memoryTotal": "85520809984", "cudaCores": 16896, "architecture": "<PERSON>", "uuid": "GPU-8f6a2715-cf0e-6220-3ad7-70072cbe7901"}], "cudaVersion": "12.4", "writerId": "fp3fosiehlujs4fbpx8ntrdqlh16j2y9"}