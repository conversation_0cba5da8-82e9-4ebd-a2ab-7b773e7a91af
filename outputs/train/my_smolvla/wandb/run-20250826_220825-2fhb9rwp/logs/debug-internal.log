{"time":"2025-08-26T22:08:25.87612953+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.1"}
{"time":"2025-08-26T22:08:26.524915903+08:00","level":"INFO","msg":"stream: created new stream","id":"2fhb9rwp"}
{"time":"2025-08-26T22:08:26.524964331+08:00","level":"INFO","msg":"stream: started","id":"2fhb9rwp"}
{"time":"2025-08-26T22:08:26.525010763+08:00","level":"INFO","msg":"sender: started","stream_id":"2fhb9rwp"}
{"time":"2025-08-26T22:08:26.525010008+08:00","level":"INFO","msg":"writer: started","stream_id":"2fhb9rwp"}
{"time":"2025-08-26T22:08:26.52505463+08:00","level":"INFO","msg":"handler: started","stream_id":"2fhb9rwp"}
{"time":"2025-08-27T00:51:12.460725904+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-08-27T00:51:28.149638719+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/xxll/lerobot/2fhb9rwp/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-08-27T02:12:53.644434605+08:00","level":"INFO","msg":"stream: closing","id":"2fhb9rwp"}
{"time":"2025-08-27T02:14:20.830570607+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-08-27T02:14:21.341536545+08:00","level":"INFO","msg":"handler: closed","stream_id":"2fhb9rwp"}
{"time":"2025-08-27T02:14:21.341674005+08:00","level":"INFO","msg":"sender: closed","stream_id":"2fhb9rwp"}
{"time":"2025-08-27T02:14:21.341707044+08:00","level":"INFO","msg":"stream: closed","id":"2fhb9rwp"}
