#!/usr/bin/env python3
"""
Helper script to fix draccus boolean parsing issues.

This script preprocesses command line arguments to convert string boolean values
to proper boolean format that draccus can understand.

Usage:
    python fix_draccus_bool.py lerobot-train --policy.push_to_hub=False --other.args=value
"""

import sys
import subprocess

def fix_boolean_args(args):
    """Convert string boolean values to proper format for draccus."""
    fixed_args = []
    
    for arg in args:
        if '=' in arg:
            key, value = arg.split('=', 1)
            # Check if this looks like a boolean parameter
            if value.lower() in ('true', 'false'):
                # Convert to lowercase for draccus
                fixed_args.append(f"{key}={value.lower()}")
            else:
                fixed_args.append(arg)
        else:
            fixed_args.append(arg)
    
    return fixed_args

def main():
    if len(sys.argv) < 2:
        print("Usage: python fix_draccus_bool.py <command> [args...]")
        sys.exit(1)
    
    # Get the original command and arguments
    original_command = sys.argv[1]
    original_args = sys.argv[2:]
    
    # Fix boolean arguments
    fixed_args = fix_boolean_args(original_args)
    
    # Construct the final command
    final_command = [original_command] + fixed_args
    
    print(f"Original command: {original_command} {' '.join(original_args)}")
    print(f"Fixed command: {' '.join(final_command)}")
    print()
    
    # Execute the command
    try:
        result = subprocess.run(final_command, check=True)
        sys.exit(result.returncode)
    except subprocess.CalledProcessError as e:
        sys.exit(e.returncode)
    except FileNotFoundError:
        print(f"Error: Command '{original_command}' not found")
        sys.exit(1)

if __name__ == "__main__":
    main()
