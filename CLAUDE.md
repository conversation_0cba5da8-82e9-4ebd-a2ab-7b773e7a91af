# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build System and Development

This project uses Python with setuptools. The main build and development commands are:

### Installation
```bash
# Install from source (editable mode for development)
pip install -e .

# Install with specific features
pip install -e ".[aloha,pusht]"  # Simulation environments
pip install -e ".[all]"          # All available features
```

### Testing
```bash
# Run all tests
pytest

# Run specific test categories
make test-end-to-end
make test-act-ete-train
make test-diffusion-ete-train

# Run with specific device
make test-end-to-end DEVICE=cuda
```

### Code Quality
```bash
# Linting and formatting
ruff check .
ruff format .

# Type checking (currently commented out in config)
# mypy src/lerobot/
```

### Training and Evaluation
```bash
# Train a policy
lerobot-train --policy.type=act --env.type=aloha --dataset.repo_id=lerobot/aloha_sim_transfer_cube_human

# Evaluate a policy
lerobot-eval --policy.path=lerobot/diffusion_pusht --env.type=pusht

# Visualize datasets
python -m lerobot.scripts.visualize_dataset --repo-id lerobot/pusht --episode-index 0
```

## Architecture Overview

LeRobot is a PyTorch-based robotics library focused on imitation learning and reinforcement learning. The main components are:

### Core Structure
- **`src/lerobot/`** - Main package directory
- **`policies/`** - Various policy implementations (ACT, Diffusion, TDMPC, VQ-BeT, SmolVLA, etc.)
- **`robots/`** - Robot-specific implementations (SO100, SO101, LeKiwi, HopeJR, etc.)
- **`datasets/`** - Dataset handling and LeRobotDataset format
- **`envs/`** - Environment factory and utilities
- **`cameras/`** - Camera support (OpenCV, Intel RealSense)
- **`motors/`** - Motor control (Dynamixel, Feetech)
- **`teleoperators/`** - Teleoperation interfaces

### Key Concepts
1. **LeRobotDataset**: Custom dataset format supporting temporal data, video frames, and multiple modalities
2. **Policy Framework**: Unified interface for different robotics policies with configurable training
3. **Robot Abstraction**: Hardware-specific implementations with common interfaces
4. **Environment Integration**: Support for both simulation and real-world environments

### Available Components
- **Environments**: aloha, pusht, xarm (simulation), koch_real, aloha_real (real-world)
- **Policies**: act, diffusion, tdmpc, vqbet, smolvla, pi0, sac
- **Robots**: koch, aloha, so100, so101, lekiwi, hopejr
- **Cameras**: opencv, intelrealsense
- **Motors**: dynamixel, feetech

### Configuration System
- Uses dataclasses for configuration with draccus for YAML parsing
- Default configurations in `src/lerobot/configs/`
- Environment-specific configurations available
- Policy-specific configurations with hyperparameters

### Dataset Format
- Stores temporal robot data with observations, actions, and metadata
- Supports video encoding (MP4) for efficient storage
- Uses Hugging Face datasets for underlying storage
- Provides frame indexing with temporal relationships via `delta_timestamps`

## Development Notes

### Code Style
- Line length: 110 characters
- Uses ruff for linting with specific rule sets (E, W, F, I, B, C4, T20, N)
- Double quotes for strings, spaces for indentation
- Google-style docstrings (currently not enforced)

### Dependencies
- Core: PyTorch, Hugging Face libraries, OpenCV, gymnasium
- Optional: transformers, grpcio, various robot-specific SDKs
- Development: pytest, pre-commit, ruff

### Testing
- End-to-end tests available for major policies
- Tests in `tests/` directory with artifacts for reproducible testing
- Mock implementations available for hardware components

### Hardware Integration
- Real robot support requires additional dependencies (e.g., `lerobot[feetech]`)
- Camera support requires platform-specific installations
- Motor control uses serial communication with appropriate SDKs